import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity, Modal} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import BookIcon from "../../components/icons/book-icon";
import styles from "@/styles/settings/my-products.style";
import {useTranslation} from "react-i18next";

interface MyProduct {
  id: string;
  type: "ebook" | "course" | "subscription" | "service";
  title: string;
  description: string;
  purchaseDate: string;
  expiryDate?: string;
  status: "active" | "expired" | "pending";
  accessUrl?: string;
  downloadUrl?: string;
}

const MyProducts: React.FC = () => {
  const {t} = useTranslation();
  const [selectedTab, setSelectedTab] = useState<string>("all");
  const [selectedProduct, setSelectedProduct] = useState<MyProduct | null>(
    null
  );
  const [products] = useState<MyProduct[]>([
    {
      id: "1",
      type: "ebook",
      title: "<PERSON><PERSON>a <PERSON> de Empreendedorismo Digital",
      description: "E-book com estratégias avançadas para empreendedores",
      purchaseDate: "2024-05-22",
      status: "active",
      downloadUrl: "https://example.com/download/ebook1"
    },
    {
      id: "2",
      type: "course",
      title: "Curso de Marketing Digital Avançado",
      description: "Curso completo com certificado",
      purchaseDate: "2024-05-21",
      expiryDate: "2025-05-21",
      status: "active",
      accessUrl: "https://example.com/course/marketing"
    },
    {
      id: "3",
      type: "subscription",
      title: "Assinatura Premium Club M",
      description: "Acesso completo a todos os benefícios",
      purchaseDate: "2024-05-20",
      expiryDate: "2025-05-20",
      status: "active"
    },
    {
      id: "4",
      type: "service",
      title: "Mentoria Individual - Negócios",
      description: "Sessão de mentoria personalizada",
      purchaseDate: "2024-04-15",
      status: "expired"
    },
    {
      id: "5",
      type: "ebook",
      title: "Manual de Investimentos para Iniciantes",
      description: "Guia prático para começar a investir",
      purchaseDate: "2024-04-10",
      status: "active",
      downloadUrl: "https://example.com/download/ebook2"
    }
  ]);

  const allTabs = [
    {id: "all", name: "Todos os produtos"},
    {id: "active", name: "Produtos ativos"},
    {id: "expired", name: "Produtos expirados"}
  ];

  // Reorder tabs to put the selected tab first
  const tabs = [
    allTabs.find((tab) => tab.id === selectedTab)!,
    ...allTabs.filter((tab) => tab.id !== selectedTab)
  ];

  const filteredProducts =
    selectedTab === "all"
      ? products
      : products.filter((product) => product.status === selectedTab);

  // Group products by month
  const groupedProducts = filteredProducts.reduce((groups, product) => {
    const date = new Date(product.purchaseDate);
    const currentDate = new Date();

    let monthKey;
    if (
      date.getMonth() === currentDate.getMonth() &&
      date.getFullYear() === currentDate.getFullYear()
    ) {
      monthKey = "Mês atual";
    } else {
      monthKey = date.toLocaleDateString("pt-BR", {
        month: "long",
        year: "numeric"
      });
      monthKey = monthKey.charAt(0).toUpperCase() + monthKey.slice(1);
    }

    if (!groups[monthKey]) {
      groups[monthKey] = [];
    }
    groups[monthKey].push(product);
    return groups;
  }, {} as Record<string, MyProduct[]>);

  const getStatusStyles = (status: string) => {
    switch (status) {
      case "active":
        return {
          container: styles.activeStatus,
          text: styles.activeText
        };
      case "expired":
        return {
          container: styles.expiredStatus,
          text: styles.expiredText
        };
      case "pending":
      default:
        return {
          container: styles.pendingStatus,
          text: styles.pendingText
        };
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Ativo";
      case "expired":
        return "Expirado";
      case "pending":
        return "Pendente";
      default:
        return status;
    }
  };

  const getProductTypeIcon = (type: string) => {
    switch (type) {
      case "ebook":
        return "📖";
      case "course":
        return "🎓";
      case "subscription":
        return "⭐";
      case "service":
        return "🤝";
      default:
        return "📄";
    }
  };

  const getProductTypeName = (type: string) => {
    switch (type) {
      case "ebook":
        return "E-book";
      case "course":
        return "Curso";
      case "subscription":
        return "Assinatura";
      case "service":
        return "Serviço";
      default:
        return "Produto";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    });
  };

  const handleProductAccess = (product: MyProduct) => {
    if (product.accessUrl) {
      // Open course or service access URL
      console.log("Opening access URL:", product.accessUrl);
    } else if (product.downloadUrl) {
      // Download e-book
      console.log("Downloading:", product.downloadUrl);
    }
  };

  return (
    <ScreenWithHeader screenTitle="Meus produtos" backButton>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[styles.tab, selectedTab === tab.id && styles.activeTab]}
                onPress={() => setSelectedTab(tab.id)}
              >
                <Text
                  style={[
                    styles.tabText,
                    selectedTab === tab.id && styles.activeTabText
                  ]}
                >
                  {tab.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Product List */}
          <ScrollView style={{flex: 1}}>
            {Object.entries(groupedProducts).map(
              ([monthKey, monthProducts], index) => (
                <View key={monthKey}>
                  {/* Month Label */}
                  <Text style={styles.monthLabel}>{monthKey}</Text>

                  {monthProducts.map((product) => (
                    <TouchableOpacity
                      key={product.id}
                      style={styles.productItem}
                      onPress={() => setSelectedProduct(product)}
                    >
                      <View style={styles.productIcon}>
                        <Text style={{fontSize: 16, color: "#FFFFFF"}}>
                          {getProductTypeIcon(product.type)}
                        </Text>
                      </View>

                      <View style={styles.productContent}>
                        <Text
                          style={styles.productTitle}
                          numberOfLines={2}
                          ellipsizeMode="tail"
                        >
                          {product.title}
                        </Text>
                        <Text style={styles.productType}>
                          {getProductTypeName(product.type)}
                        </Text>
                      </View>

                      <View style={styles.productRight}>
                        <Text
                          style={[
                            styles.statusText,
                            getStatusStyles(product.status).text
                          ]}
                        >
                          {getStatusText(product.status)}
                        </Text>
                        {product.status === "active" && (
                          <TouchableOpacity
                            style={styles.accessButton}
                            onPress={(e) => {
                              e.stopPropagation();
                              handleProductAccess(product);
                            }}
                          >
                            <Text style={styles.accessButtonText}>
                              {product.type === "ebook" ? "Baixar" : "Acessar"}
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )
            )}
          </ScrollView>
        </View>

        {/* Detail Modal */}
        <Modal
          visible={selectedProduct !== null}
          transparent
          animationType="slide"
          onRequestClose={() => setSelectedProduct(null)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setSelectedProduct(null)}
          >
            <TouchableOpacity
              style={styles.modalContent}
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
            >
              {/* Drag Indicator */}
              <View style={styles.dragIndicator} />

              {/* Header */}
              <Text style={styles.modalTitle}>Detalhes do produto</Text>

              {selectedProduct && (
                <ScrollView
                  style={styles.modalScrollView}
                  showsVerticalScrollIndicator={false}
                >
                  {/* Product Icon */}
                  <View style={styles.iconContainer}>
                    <BookIcon width={20} height={20} replaceColor="#EAECF0" />
                  </View>

                  {/* Product Title */}
                  <Text style={styles.productTitleModal}>
                    {selectedProduct.title}
                  </Text>

                  {/* General Information Section */}
                  <View style={styles.infoSection}>
                    <Text style={styles.infoSectionTitle}>
                      Informações gerais
                    </Text>

                    <View style={[styles.infoRow, styles.infoRowHighlighted]}>
                      <Text style={styles.infoLabel}>Status</Text>
                      <Text
                        style={[
                          styles.infoValue,
                          getStatusStyles(selectedProduct.status).text
                        ]}
                      >
                        {getStatusText(selectedProduct.status)}
                      </Text>
                    </View>

                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>Tipo de produto</Text>
                      <Text style={styles.infoValue}>
                        {getProductTypeName(selectedProduct.type)}
                      </Text>
                    </View>

                    <View style={[styles.infoRow, styles.infoRowHighlighted]}>
                      <Text style={styles.infoLabel}>Data de aquisição</Text>
                      <Text style={styles.infoValue}>
                        {formatDate(selectedProduct.purchaseDate)}
                      </Text>
                    </View>

                    {selectedProduct.expiryDate && (
                      <View style={styles.infoRow}>
                        <Text style={styles.infoLabel}>Data de expiração</Text>
                        <Text style={styles.infoValue}>
                          {formatDate(selectedProduct.expiryDate)}
                        </Text>
                      </View>
                    )}

                    <View style={[styles.infoRow, styles.infoRowHighlighted]}>
                      <Text style={styles.infoLabel}>Descrição</Text>
                      <Text
                        style={[styles.infoValue, styles.infoValueMultiline]}
                      >
                        {selectedProduct.description}
                      </Text>
                    </View>
                  </View>

                  {/* Action Buttons */}
                  {selectedProduct.status === "active" && (
                    <View style={styles.actionButtonsContainer}>
                      <TouchableOpacity
                        style={styles.primaryActionButton}
                        onPress={() => {
                          handleProductAccess(selectedProduct);
                          setSelectedProduct(null);
                        }}
                      >
                        <Text style={styles.primaryActionButtonText}>
                          {selectedProduct.type === "ebook"
                            ? "Baixar produto"
                            : "Acessar produto"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}

                  {/* Close Button */}
                  <TouchableOpacity
                    style={styles.closeButtonModal}
                    onPress={() => setSelectedProduct(null)}
                  >
                    <Text style={styles.closeButtonTextModal}>Fechar</Text>
                  </TouchableOpacity>
                </ScrollView>
              )}
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </View>
    </ScreenWithHeader>
  );
};

export default MyProducts;
