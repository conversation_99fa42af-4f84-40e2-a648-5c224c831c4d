import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingTop: 8
  },
  tabContainer: {
    flexDirection: "row",
    gap: 16,
    paddingLeft: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085"
  },
  tab: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    paddingTop: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "flex-start",
    borderBottomWidth: 2,
    borderBottomColor: "transparent"
  },
  activeTab: {
    borderBottomColor: "#FCFCFD",
    borderBottomWidth: 2
  },
  tabText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.7
  },
  activeTabText: {
    fontWeight: "700",
    opacity: 1,
    color: "#FCFCFD"
  },
  monthLabel: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginTop: 12,
    marginLeft: 24,
    marginRight: 24,
    minHeight: 18
  },
  productItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 4,
    marginLeft: 24,
    marginRight: 24,
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  productIcon: {
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 2,
    marginBottom: 2
  },
  productContent: {
    flex: 1,
    justifyContent: "center",
    minHeight: 36
  },
  productTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    lineHeight: 18,
    flex: 1,
    marginRight: 8,
    marginBottom: 2
  },
  productType: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 14
  },
  productRight: {
    gap: 4,
    flexDirection: "column",
    alignItems: "flex-end",
    minWidth: 60
  },
  statusText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    lineHeight: 14,
    minHeight: 14,
    textAlign: "right"
  },
  accessButton: {
    backgroundColor: "#2563EB",
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 50
  },
  accessButtonText: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "600",
    lineHeight: 14,
    textAlign: "center"
  },
  // Status colors
  pendingStatus: {
    backgroundColor: "transparent"
  },
  pendingText: {
    color: "#FDB022"
  },
  activeStatus: {
    backgroundColor: "transparent"
  },
  activeText: {
    color: "#47CD89"
  },
  expiredStatus: {
    backgroundColor: "transparent"
  },
  expiredText: {
    color: "#F97066"
  }
});

export default styles;
